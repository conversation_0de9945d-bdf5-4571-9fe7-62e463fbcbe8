import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hekimmind/data/models/analysis_result_model.dart';

class AnalysisResultCard extends StatelessWidget {
  final AnalysisResultModel result;
  final VoidCallback? onShare;

  const AnalysisResultCard({
    Key? key,
    required this.result,
    this.onShare,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 20),
            _buildImageSection(context),
            const SizedBox(height: 20),
            _buildAnalysisSection(context),
            const SizedBox(height: 20),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getAnalysisTypeColor(),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            result.analysisType,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        const Spacer(),
        Text(
          _formatDate(result.timestamp),
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.network(
          result.imageUrl,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[100],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('Görüntü yüklenemedi'),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAnalysisSection(BuildContext context) {
    // Boş sonuç kontrolü
    if (result.result.trim().isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analiz Sonucu',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange.shade600,
                  size: 48,
                ),
                const SizedBox(height: 12),
                Text(
                  'Analiz Sonucu Boş',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'OpenAI API\'den boş yanıt alındı. Lütfen tekrar deneyin.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange.shade700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      );
    }

    final sections = _parseAnalysisResult(result.result);

    // Parsing başarısız olursa ham metni göster
    if (sections.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analiz Sonucu',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.description,
                      color: Colors.blue.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Ham Analiz Sonucu',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  result.result,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analiz Sonucu',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...sections.map((section) => _buildSection(context, section)),
      ],
    );
  }

  Widget _buildSection(BuildContext context, Map<String, String> section) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getSectionIcon(section['title']!),
                color: _getSectionColor(section['title']!),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                section['title']!,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            section['content']!,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _copyToClipboard(context),
            icon: const Icon(Icons.copy),
            label: const Text('Kopyala'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onShare,
            icon: const Icon(Icons.share),
            label: const Text('Paylaş'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getAnalysisTypeColor() {
    switch (result.analysisType.toUpperCase()) {
      case 'MR':
        return Colors.purple;
      case 'EKG':
        return Colors.red;
      case 'RÖNTGEN':
        return Colors.blue;
      case 'TOMOGRAFİ':
        return Colors.orange;
      case 'DERİ':
        return Colors.green;
      case 'ULTRASON':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getSectionIcon(String title) {
    if (title.contains('GENEL') || title.contains('DEĞERLENDİRME')) {
      return Icons.assessment;
    } else if (title.contains('BULGULAR')) {
      return Icons.search;
    } else if (title.contains('ÖNERİLER')) {
      return Icons.lightbulb;
    } else if (title.contains('UYARI')) {
      return Icons.warning;
    }
    return Icons.info;
  }

  Color _getSectionColor(String title) {
    if (title.contains('GENEL') || title.contains('DEĞERLENDİRME')) {
      return Colors.blue;
    } else if (title.contains('BULGULAR')) {
      return Colors.orange;
    } else if (title.contains('ÖNERİLER')) {
      return Colors.green;
    } else if (title.contains('UYARI')) {
      return Colors.red;
    }
    return Colors.grey;
  }

  List<Map<String, String>> _parseAnalysisResult(String result) {
    final sections = <Map<String, String>>[];
    final lines = result.split('\n');
    
    String? currentTitle;
    StringBuffer currentContent = StringBuffer();
    
    for (String line in lines) {
      line = line.trim();
      if (line.startsWith('**') && line.endsWith('**')) {
        // Önceki bölümü kaydet
        if (currentTitle != null && currentContent.toString().trim().isNotEmpty) {
          sections.add({
            'title': currentTitle,
            'content': currentContent.toString().trim(),
          });
        }
        
        // Yeni bölüm başlat
        currentTitle = line.replaceAll('**', '').replaceAll(':', '');
        currentContent = StringBuffer();
      } else if (line.isNotEmpty && currentTitle != null) {
        if (currentContent.length > 0) {
          currentContent.writeln();
        }
        currentContent.write(line);
      }
    }
    
    // Son bölümü kaydet
    if (currentTitle != null && currentContent.toString().trim().isNotEmpty) {
      sections.add({
        'title': currentTitle,
        'content': currentContent.toString().trim(),
      });
    }
    
    return sections;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.'
           '${date.month.toString().padLeft(2, '0')}.'
           '${date.year} '
           '${date.hour.toString().padLeft(2, '0')}:'
           '${date.minute.toString().padLeft(2, '0')}';
  }

  void _copyToClipboard(BuildContext context) {
    final text = '''
${result.analysisType} Analizi
Tarih: ${_formatDate(result.timestamp)}

${result.result}
''';
    
    Clipboard.setData(ClipboardData(text: text));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Analiz sonucu panoya kopyalandı'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
